//
//  DownloadsView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

#if !os(tvOS)
struct DownloadsView: View {
    @ObservedObject private var downloadService = VideoDownloadService.shared
    @State private var selectedDownload: VideoDownloadInfo?
    @State private var showingDeleteAlert = false
    
    var body: some View {
        VStack {
                if downloadService.activeDownloads.isEmpty &&
                   downloadService.completedDownloads.isEmpty {
                    // Empty state
                    VStack(spacing: 16) {
                        Image(systemName: "arrow.down.circle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No Downloads")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("Downloaded videos will appear here")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List {
                        // Active Downloads Section
                        if !downloadService.activeDownloads.isEmpty {
                            Section("Active Downloads") {
                                ForEach(Array(downloadService.activeDownloads.values), id: \.video.id) { download in
                                    ActiveDownloadRow(
                                        download: download,
                                        onPause: {
                                            downloadService.pauseDownload(for: download.video.id)
                                        },
                                        onResume: {
                                            downloadService.resumeDownload(for: download.video.id)
                                        },
                                        onCancel: {
                                            downloadService.cancelDownload(for: download.video.id)
                                        }
                                    )
                                }
                            }
                        }
                        
                        // Completed Downloads Section
                        if !downloadService.completedDownloads.isEmpty {
                            Section("Downloaded Videos") {
                                ForEach(Array(downloadService.completedDownloads.values), id: \.video.id) { download in
                                    CompletedDownloadRow(
                                        download: download,
                                        fileURL: downloadService.getDownloadedFileURL(for: download),
                                        onDelete: {
                                            selectedDownload = download
                                            showingDeleteAlert = true
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
        }
        .refreshable {
            // Refresh the downloads list
            downloadService.objectWillChange.send()
        }
        .alert("Delete Download", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                if let download = selectedDownload {
                    try? downloadService.deleteDownloadedVideo(for: download.video.id)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            if let download = selectedDownload {
                Text("Are you sure you want to delete '\(download.video.title)'?")
            }
        }
    }
}

// MARK: - Shared Components

struct DownloadThumbnailView: View {
    let imageUrl: URL?

    var body: some View {
        CachedAsyncImage(url: imageUrl) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
        }
        .frame(width: 80, height: 45)
        .clipShape(RoundedRectangle(cornerRadius: 6))
    }
}

struct DownloadVideoInfoView: View {
    let video: UIVideo
    let additionalContent: AnyView?

    init(video: UIVideo, @ViewBuilder additionalContent: () -> some View = { EmptyView() }) {
        self.video = video
        self.additionalContent = AnyView(additionalContent())
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(video.title)
                .font(.headline)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            if let channel = video.channel {
                Text(channel.name)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            additionalContent
        }
    }
}

struct CircularProgressView: View {
    let progress: Double

    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 3)
                .frame(width: 32, height: 32)

            Circle()
                .trim(from: 0, to: progress)
                .stroke(.chzzk, style: StrokeStyle(lineWidth: 3, lineCap: .round))
                .frame(width: 32, height: 32)
                .rotationEffect(.degrees(-90))
        }
    }
}

// MARK: - Row Components

struct ActiveDownloadRow: View {
    let download: VideoDownloadInfo
    let onPause: () -> Void
    let onResume: () -> Void
    let onCancel: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            DownloadThumbnailView(imageUrl: download.video.imageUrl)

            DownloadVideoInfoView(video: download.video) {
                // Download Progress
                VStack(alignment: .leading, spacing: 2) {
                    Text(download.state.displayText)
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }

            Spacer()

            // Progress and Control Buttons
            HStack(spacing: 8) {
                // Circular progress indicator
                if case .downloading(let progress) = download.state {
                    CircularProgressView(progress: progress)
                } else if case .paused(let progress) = download.state {
                    CircularProgressView(progress: progress)
                } else if case .processing = download.state {
                    ProgressView()
                        .progressViewStyle(.circular)
                        .scaleEffect(0.8)
                        .frame(width: 32, height: 32)
                }

                // Pause/Resume Button
                if case .downloading = download.state {
                    Button(action: onPause) {
                        Image(systemName: "pause.circle.fill")
                            .font(.title2)
                            .foregroundColor(.orange)
                    }
                    .buttonStyle(PlainButtonStyle())
                } else if case .paused = download.state {
                    Button(action: onResume) {
                        Image(systemName: "play.circle.fill")
                            .font(.title2)
                            .foregroundColor(.green)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(.vertical, 4)
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
            Button("Cancel", role: .destructive) {
                onCancel()
            }
            .tint(.red)
        }
    }
}

struct QualityBadgeView: View {
    let quality: VideoQuality
    let fileSize: String

    var body: some View {
        HStack {
            Text(quality.displayName)
                .font(.caption)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .clipShape(RoundedRectangle(cornerRadius: 4))

            Text(fileSize)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()
        }
    }
}

struct CompletedDownloadRow: View {
    let download: VideoDownloadInfo
    let fileURL: URL?
    let onDelete: () -> Void

    private var downloadRowContent: some View {
        HStack(spacing: 12) {
            DownloadThumbnailView(imageUrl: download.video.imageUrl)

            DownloadVideoInfoView(video: download.video) {
                QualityBadgeView(quality: download.quality, fileSize: download.formattedFileSize)
            }
        }
    }

    var body: some View {
        Group {
            if let fileURL = fileURL {
                ShareLink(item: fileURL) {
                    downloadRowContent
                }
            } else {
                // Fallback if file URL is not available
                Button(action: {
                    VideoDownloadService.shared.openFilesApp()
                }) {
                    downloadRowContent
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .foregroundStyle(.primary)
        .padding(.vertical, 4)
        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
            .tint(.red)
        }
        .contextMenu {
            Button(action: onDelete) {
                Label("Delete", systemImage: "trash")
            }
            .tint(.red)
        }
        .onAppear {
            // Update file size when the row appears if not already set
            if download.totalBytes == 0 {
                VideoDownloadService.shared.updateFileSizeForCompletedDownload(videoId: download.video.id)
            }
        }
    }
}

// MARK: - Preview Helpers

struct DownloadPreviewData {
    static func createSampleVideo(
        id: Int,
        title: String,
        channelName: String,
        category: String = "Gaming"
    ) -> UIVideo {
        UIVideo(
            id: id,
            uid: "sample-uid-\(id)",
            title: title,
            imageUrl: URL(string: "https://picsum.photos/400/250"),
            duration: 3600,
            timestamp: Date().timeIntervalSince1970 - TimeInterval(id * 86400),
            category: category,
            channel: UIChannel(
                id: "channel-\(id)",
                name: channelName,
                imageUrl: URL(string: "https://picsum.photos/200")
            ),
            progress: nil
        )
    }

    static func createSampleQuality(_ resolution: String) -> VideoQuality {
        let resolutionInt = Int(resolution.replacingOccurrences(of: "p", with: "")) ?? 1080
        return VideoQuality(
            id: resolution,
            resolution: resolutionInt,
            bandwidth: resolutionInt * 5000,
            url: URL(string: "https://example.com/video.m3u8")!
        )
    }

    static func createDownloadInfo(
        video: UIVideo,
        quality: VideoQuality,
        state: VideoDownloadState,
        totalMB: Int64,
        downloadedMB: Int64
    ) -> VideoDownloadInfo {
        VideoDownloadInfo(
            video: video,
            quality: quality,
            downloadURL: URL(string: "https://example.com/download.ts")!,
            destinationURL: URL(string: "file:///tmp/video.ts")!,
            state: state,
            startTime: Date().addingTimeInterval(-300),
            totalBytes: totalMB * 1024 * 1024,
            downloadedBytes: downloadedMB * 1024 * 1024
        )
    }
}

#Preview("Empty View") {
    DownloadsView()
}

#Preview("Downloading") {
    let video = DownloadPreviewData.createSampleVideo(
        id: 12345,
        title: "Sample Video Title That Is Very Long To Test Truncation In Download Rows",
        channelName: "Sample Channel"
    )

    let downloadInfo = DownloadPreviewData.createDownloadInfo(
        video: video,
        quality: DownloadPreviewData.createSampleQuality("1080p"),
        state: .downloading(progress: 0.65),
        totalMB: 500,
        downloadedMB: 325
    )

    ActiveDownloadRow(
        download: downloadInfo,
        onPause: { print("Pause tapped") },
        onResume: { print("Resume tapped") },
        onCancel: { print("Cancel tapped") }
    )
    .padding()
}

#Preview("Paused") {
    let video = DownloadPreviewData.createSampleVideo(
        id: 12346,
        title: "Paused Download Video Title",
        channelName: "Sample Channel"
    )

    let downloadInfo = DownloadPreviewData.createDownloadInfo(
        video: video,
        quality: DownloadPreviewData.createSampleQuality("720p"),
        state: .paused(progress: 0.45),
        totalMB: 300,
        downloadedMB: 135
    )

    ActiveDownloadRow(
        download: downloadInfo,
        onPause: { print("Pause tapped") },
        onResume: { print("Resume tapped") },
        onCancel: { print("Cancel tapped") }
    )
    .padding()
}

#Preview("Downloaded") {
    let video = DownloadPreviewData.createSampleVideo(
        id: 12348,
        title: "Completed Download Video With Very Long Title That Should Wrap",
        channelName: "Sports Channel",
        category: "Sports"
    )

    let downloadInfo = DownloadPreviewData.createDownloadInfo(
        video: video,
        quality: DownloadPreviewData.createSampleQuality("1080p"),
        state: .completed(URL(string: "file:///tmp/completed_video.ts")!),
        totalMB: 800,
        downloadedMB: 800
    )

    CompletedDownloadRow(
        download: downloadInfo,
        fileURL: URL(string: "file:///tmp/completed_video.ts")!
    ) {
        print("Delete tapped")
    }
    .padding()
}

#endif
